@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VS Code Insiders Complete Reset Script
echo ========================================
echo.
echo This script will:
echo - Close VS Code Insiders if running
echo - Remove ALL extensions (including Augment)
echo - Clear all settings, cache, and user data
echo - Remove all traces of Augment
echo - Reset VS Code Insiders to factory state
echo.

set /p confirm="Are you sure you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b
)

echo.
echo Starting reset process...

REM Close VS Code Insiders if running
echo Closing VS Code Insiders...
taskkill /f /im "Code - Insiders.exe" >nul 2>&1

REM Wait a moment for processes to close
timeout /t 3 /nobreak >nul

REM Define paths
set "VSCODE_INSIDERS_PATH=%APPDATA%\Code - Insiders"
set "VSCODE_INSIDERS_LOCAL=%LOCALAPPDATA%\Programs\Microsoft VS Code Insiders"
set "CODE_CMD=%VSCODE_INSIDERS_LOCAL%\bin\code-insiders.cmd"

echo.
echo Removing all extensions...
if exist "%CODE_CMD%" (
    REM Get list of all extensions and remove them
    for /f "tokens=*" %%i in ('"%CODE_CMD%" --list-extensions 2^>nul') do (
        echo Removing extension: %%i
        "%CODE_CMD%" --uninstall-extension "%%i" --force >nul 2>&1
    )
) else (
    echo VS Code Insiders executable not found, skipping extension removal.
)

echo.
echo Removing VS Code Insiders user data and settings...
if exist "%VSCODE_INSIDERS_PATH%" (
    echo Removing directory: %VSCODE_INSIDERS_PATH%
    rmdir /s /q "%VSCODE_INSIDERS_PATH%" >nul 2>&1
    if exist "%VSCODE_INSIDERS_PATH%" (
        echo Warning: Some files may still be in use. Trying alternative removal...
        rd /s /q "%VSCODE_INSIDERS_PATH%" >nul 2>&1
    )
)

REM Remove any Augment-related files from temp directories
echo.
echo Cleaning temporary files and Augment traces...
if exist "%TEMP%\augment*" (
    echo Removing Augment temp files...
    del /f /q "%TEMP%\augment*" >nul 2>&1
    rmdir /s /q "%TEMP%\augment*" >nul 2>&1
)

REM Clean Windows Registry entries for VS Code Insiders (optional)
echo.
echo Cleaning registry entries...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\{1287CAD5-7C8D-410D-9DAE-1B6B045A7C3B}_is1" /f >nul 2>&1
reg delete "HKCU\Software\Classes\Applications\Code - Insiders.exe" /f >nul 2>&1

REM Remove any cached extension data
if exist "%LOCALAPPDATA%\Microsoft\Windows\INetCache\IE\*augment*" (
    echo Removing cached Augment data...
    del /f /q "%LOCALAPPDATA%\Microsoft\Windows\INetCache\IE\*augment*" >nul 2>&1
)

REM Clear Windows Search index for VS Code Insiders files
echo Clearing search index...
del /f /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\*Code*Insiders*" >nul 2>&1

echo.
echo ========================================
echo Reset Complete!
echo ========================================
echo.
echo VS Code Insiders has been completely reset:
echo - All extensions removed (including Augment)
echo - All settings and configurations cleared
echo - All cache and temporary files removed
echo - All user data removed
echo - Registry entries cleaned
echo.
echo When you next start VS Code Insiders, it will be
echo in a completely fresh state as if newly installed.
echo.
echo You can now reinstall only the extensions you need.
echo.

pause
