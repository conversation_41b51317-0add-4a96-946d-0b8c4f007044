@echo off
setlocal enabledelayedexpansion

echo ========================================
echo VS Code Insiders Augment Removal Script
echo ========================================
echo.
echo This script will:
echo - Close VS Code Insiders if running
echo - Remove ONLY Augment extension and related extensions
echo - Clear Augment-specific cache and temporary files
echo - Keep all other extensions and settings intact
echo - Remove all traces of Augment from the system
echo.

set /p confirm="Are you sure you want to proceed? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b
)

echo.
echo Starting Augment removal process...

REM Close VS Code Insiders if running
echo Closing VS Code Insiders...
taskkill /f /im "Code - Insiders.exe" >nul 2>&1

REM Wait a moment for processes to close
timeout /t 3 /nobreak >nul

REM Define paths
set "VSCODE_INSIDERS_PATH=%APPDATA%\Code - Insiders"
set "VSCODE_INSIDERS_LOCAL=%LOCALAPPDATA%\Programs\Microsoft VS Code Insiders"
set "CODE_CMD=%VSCODE_INSIDERS_LOCAL%\bin\code-insiders.cmd"

echo.
echo Removing Augment extensions only...
if exist "%CODE_CMD%" (
    REM List of Augment-related extensions to remove
    set "augment_extensions=augment.vscode-augment"

    REM Check and remove Augment extensions
    for %%ext in (!augment_extensions!) do (
        echo Checking for extension: %%ext
        "%CODE_CMD%" --list-extensions | findstr /i "%%ext" >nul 2>&1
        if !errorlevel! equ 0 (
            echo Removing Augment extension: %%ext
            "%CODE_CMD%" --uninstall-extension "%%ext" --force
        ) else (
            echo Extension %%ext not found or already removed
        )
    )
) else (
    echo VS Code Insiders executable not found, skipping extension removal.
)

echo.
echo Removing Augment-specific data only (keeping other settings)...
REM Remove only Augment-related directories and files
if exist "%VSCODE_INSIDERS_PATH%\User\workspaceStorage" (
    echo Checking workspace storage for Augment data...
    for /d %%d in ("%VSCODE_INSIDERS_PATH%\User\workspaceStorage\*") do (
        if exist "%%d\*augment*" (
            echo Removing Augment workspace data from: %%d
            del /f /q "%%d\*augment*" >nul 2>&1
        )
    )
)

REM Remove Augment-specific extension data
if exist "%VSCODE_INSIDERS_PATH%\User\extensions" (
    echo Removing Augment extension folders...
    for /d %%d in ("%VSCODE_INSIDERS_PATH%\User\extensions\*augment*") do (
        echo Removing: %%d
        rmdir /s /q "%%d" >nul 2>&1
    )
)

REM Remove any Augment-related files from temp directories
echo.
echo Cleaning temporary files and Augment traces...
if exist "%TEMP%\augment*" (
    echo Removing Augment temp files...
    del /f /q "%TEMP%\augment*" >nul 2>&1
    rmdir /s /q "%TEMP%\augment*" >nul 2>&1
)

REM Clean only Augment-related registry entries (keep VS Code Insiders entries)
echo.
echo Cleaning Augment-specific registry entries...
REM Note: Only removing Augment-specific entries, keeping VS Code Insiders intact

REM Remove any cached extension data
if exist "%LOCALAPPDATA%\Microsoft\Windows\INetCache\IE\*augment*" (
    echo Removing cached Augment data...
    del /f /q "%LOCALAPPDATA%\Microsoft\Windows\INetCache\IE\*augment*" >nul 2>&1
)

REM Clear Windows Search index for VS Code Insiders files
echo Clearing search index...
del /f /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\*Code*Insiders*" >nul 2>&1

echo.
echo ========================================
echo Augment Removal Complete!
echo ========================================
echo.
echo Augment has been completely removed from VS Code Insiders:
echo - Augment extension uninstalled
echo - Augment-specific cache and temporary files removed
echo - Augment workspace data cleared
echo - Augment extension folders removed
echo - Other extensions and settings preserved
echo.
echo VS Code Insiders will continue to work normally with
echo all your other extensions and settings intact.
echo.
echo Augment has been completely eliminated from your system.
echo.

pause
