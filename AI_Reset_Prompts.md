# AI Prompts for VS Code Insiders Complete Reset

This document contains detailed prompts you can use with any AI assistant to completely reset VS Code Insiders and remove all traces of Augment extension.

## Main Reset Prompt

```
I need you to help me completely reset VS Code Insiders to a factory-fresh state and remove all traces of the Augment extension. Here's what I need:

**Primary Objectives:**
1. Close VS Code Insiders if it's currently running
2. Remove ALL extensions (including Augment and any Augment-related extensions)
3. Clear all VS Code Insiders settings, configurations, and user data
4. Remove all cache files, temporary files, and any traces of Augment
5. Clean registry entries related to VS Code Insiders
6. Ensure VS Code Insiders appears as if it was never used before

**Specific Requirements:**
- Target: VS Code Insiders (not regular VS Code)
- Platform: Windows
- Remove everything in: %APPDATA%\Code - Insiders
- Clear all extensions without exception
- Remove any Augment-related files from temp directories
- Clean Windows registry entries for VS Code Insiders
- Clear any cached extension data
- Remove search index entries

**Expected Outcome:**
When VS Code Insiders is next opened, it should:
- Show the welcome screen as if first-time use
- Have no extensions installed
- Have default settings
- Show no trace of previous usage or Augment installation

Please provide step-by-step commands or a script to accomplish this.
```

## Alternative Detailed Prompt

```
Create a comprehensive solution to completely reset VS Code Insiders on Windows:

**Context:**
- I want to remove all traces of usage from VS Code Insiders
- Specifically need to eliminate all Augment extension traces
- Need it to be as if VS Code Insiders was never installed/used

**Technical Details:**
- VS Code Insiders location: %LOCALAPPDATA%\Programs\Microsoft VS Code Insiders
- User data location: %APPDATA%\Code - Insiders
- Extensions location: %APPDATA%\Code - Insiders\User\extensions
- Command line tool: code-insiders.cmd

**Required Actions:**
1. Process termination: Kill any running VS Code Insiders processes
2. Extension removal: Uninstall all extensions using --uninstall-extension
3. Data removal: Delete entire %APPDATA%\Code - Insiders directory
4. Cache cleanup: Remove temporary files and cached data
5. Registry cleanup: Remove VS Code Insiders registry entries
6. Search index cleanup: Clear Windows search index entries

**Deliverable:**
Provide either:
- A PowerShell script, OR
- A batch file (.bat), OR  
- Step-by-step manual commands

The solution should be safe, thorough, and leave VS Code Insiders in a pristine state.
```

## Troubleshooting Prompt

```
I'm having issues completely resetting VS Code Insiders. Some traces remain after attempting a reset:

**Current Issues:**
- Some extensions still appear after removal attempts
- Settings/preferences persist after clearing
- Augment extension traces still visible
- VS Code Insiders doesn't show welcome screen

**System Info:**
- Windows 10/11
- VS Code Insiders installed via standard installer
- Multiple extensions were previously installed

**What I've Tried:**
- Basic uninstall of extensions
- Deleting some user folders
- Restarting VS Code Insiders

**Need Help With:**
1. Force removal of stubborn extensions
2. Complete elimination of all user data
3. Registry cleanup procedures
4. Verification that reset was successful

Please provide a robust solution that handles edge cases and ensures complete removal.
```

## Verification Prompt

```
After performing a VS Code Insiders reset, help me verify it was successful:

**Verification Checklist:**
1. Check that no extensions are installed: `code-insiders --list-extensions`
2. Verify user data directory is clean: Check %APPDATA%\Code - Insiders
3. Confirm settings are default when VS Code Insiders opens
4. Ensure welcome screen appears on startup
5. Verify no Augment-related files exist anywhere on system

**Commands to Run:**
Please provide specific commands to:
- List current extensions
- Check directory contents
- Verify registry entries are removed
- Search for any remaining Augment files

**Success Criteria:**
- Zero extensions listed
- Default/empty settings.json
- Welcome screen on startup
- No Augment traces in file system
- Clean registry state

Provide a comprehensive verification script or command sequence.
```

## Quick Reset Prompt

```
Quick request: Create a one-click solution (batch file or PowerShell script) that completely resets VS Code Insiders on Windows to factory state. Must remove all extensions, settings, cache, and especially all Augment extension traces. Should work even if VS Code Insiders is currently running.
```

## Usage Instructions

1. **Choose the appropriate prompt** based on your needs:
   - Use "Main Reset Prompt" for comprehensive assistance
   - Use "Alternative Detailed Prompt" for technical implementation
   - Use "Troubleshooting Prompt" if you've tried resetting but issues remain
   - Use "Verification Prompt" to confirm reset was successful
   - Use "Quick Reset Prompt" for a simple, direct request

2. **Copy and paste** the chosen prompt to your AI assistant

3. **Follow the provided instructions** carefully

4. **Run verification commands** to ensure the reset was successful

## Important Notes

- Always close VS Code Insiders before running reset procedures
- Back up any important settings or extensions you want to keep
- The reset is irreversible - all data will be permanently lost
- Some operations may require administrator privileges
- Restart your computer if files seem locked or in use
