# AI Prompts for VS Code Insiders Augment Removal

This document contains detailed prompts you can use with any AI assistant to remove all traces of the Augment extension from VS Code Insiders while keeping all other extensions and settings intact.

## Main Augment Removal Prompt

```
I need you to help me completely remove the Augment extension from VS Code Insiders while keeping all other extensions and settings intact. Here's what I need:

**Primary Objectives:**
1. Close VS Code Insiders if it's currently running
2. Remove ONLY the Augment extension (keep all other extensions)
3. Clear Augment-specific cache, temporary files, and workspace data
4. Remove Augment extension folders and related files
5. Keep all other VS Code Insiders settings and configurations intact
6. Ensure no traces of Augment remain while preserving everything else

**Specific Requirements:**
- Target: VS Code Insiders (not regular VS Code)
- Platform: Windows
- Remove ONLY Augment extension: augment.vscode-augment
- Keep all other extensions and settings intact
- Remove Augment-specific files from temp directories
- Clear Augment workspace data and cache
- Remove Augment extension folders
- Preserve all other VS Code Insiders data

**Expected Outcome:**
When VS Code Insiders is next opened, it should:
- Function normally with all other extensions intact
- Have all settings and configurations preserved
- Show no trace of Augment extension
- All other extensions should work as before

Please provide step-by-step commands or a script to accomplish this.
```

## Alternative Detailed Prompt

```
Create a comprehensive solution to remove only the Augment extension from VS Code Insiders on Windows:

**Context:**
- I want to remove all traces of the Augment extension from VS Code Insiders
- Need to keep all other extensions and settings intact
- Should preserve all other VS Code Insiders functionality

**Technical Details:**
- VS Code Insiders location: %LOCALAPPDATA%\Programs\Microsoft VS Code Insiders
- User data location: %APPDATA%\Code - Insiders
- Extensions location: %APPDATA%\Code - Insiders\User\extensions
- Command line tool: code-insiders.cmd
- Target extension: augment.vscode-augment

**Required Actions:**
1. Process termination: Kill any running VS Code Insiders processes
2. Extension removal: Uninstall ONLY Augment extension using --uninstall-extension
3. Selective cleanup: Remove only Augment-related files and folders
4. Cache cleanup: Remove only Augment-specific temporary files and cache
5. Workspace cleanup: Remove Augment data from workspace storage
6. Preserve everything else: Keep all other extensions, settings, and data

**Deliverable:**
Provide either:
- A PowerShell script, OR
- A batch file (.bat), OR
- Step-by-step manual commands

The solution should be safe, selective, and only remove Augment while preserving everything else.
```

## Troubleshooting Prompt

```
I'm having issues completely removing the Augment extension from VS Code Insiders. Some traces remain after attempting removal:

**Current Issues:**
- Augment extension still appears after removal attempts
- Augment-related files or cache still visible
- Extension seems to reinstall or reappear
- Other extensions are working fine (want to keep them)

**System Info:**
- Windows 10/11
- VS Code Insiders installed via standard installer
- Multiple extensions installed (want to keep all except Augment)

**What I've Tried:**
- Basic uninstall of Augment extension
- Restarting VS Code Insiders
- Manual deletion of some Augment files

**Need Help With:**
1. Force removal of stubborn Augment extension
2. Complete elimination of all Augment traces
3. Selective cleanup that preserves other extensions
4. Verification that Augment is completely gone

Please provide a robust solution that handles edge cases and ensures complete Augment removal while preserving everything else.
```

## Verification Prompt

```
After removing the Augment extension from VS Code Insiders, help me verify it was completely removed:

**Verification Checklist:**
1. Check that Augment is not in extensions list: `code-insiders --list-extensions`
2. Verify no Augment folders remain in extensions directory
3. Confirm no Augment-related files in workspace storage
4. Ensure other extensions are still intact and working
5. Verify no Augment-related files exist anywhere on system

**Commands to Run:**
Please provide specific commands to:
- List current extensions (should show all except Augment)
- Check extension directory contents
- Search for any remaining Augment files
- Verify other extensions still work

**Success Criteria:**
- Augment extension not listed in extensions
- All other extensions still present and working
- No Augment traces in file system
- VS Code Insiders functions normally
- Settings and configurations preserved

Provide a comprehensive verification script or command sequence.
```

## Quick Removal Prompt

```
Quick request: Create a one-click solution (batch file or PowerShell script) that removes ONLY the Augment extension from VS Code Insiders on Windows. Must remove all Augment traces while keeping all other extensions, settings, and data intact. Should work even if VS Code Insiders is currently running.
```

## Usage Instructions

1. **Choose the appropriate prompt** based on your needs:
   - Use "Main Augment Removal Prompt" for comprehensive assistance
   - Use "Alternative Detailed Prompt" for technical implementation
   - Use "Troubleshooting Prompt" if you've tried removing Augment but issues remain
   - Use "Verification Prompt" to confirm Augment removal was successful
   - Use "Quick Removal Prompt" for a simple, direct request

2. **Copy and paste** the chosen prompt to your AI assistant

3. **Follow the provided instructions** carefully

4. **Run verification commands** to ensure Augment was completely removed

## Important Notes

- Always close VS Code Insiders before running removal procedures
- This process only removes Augment - all other extensions and settings are preserved
- The Augment removal is irreversible - Augment data will be permanently lost
- Your other extensions, settings, and configurations will remain intact
- Some operations may require administrator privileges
- Restart your computer if files seem locked or in use
